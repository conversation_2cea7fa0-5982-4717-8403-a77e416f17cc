"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu } from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import FormHeader from "./FormHeader";

// Optional helper to map simple rule objects to RHF rules
// rule example: { required: 'Name is required', minLength: 2, pattern: /.../, message: '...' }
const mapRulesToRHF = (rule) => {
  if (!rule) return undefined;
  const rhf = {};
  if (rule.required) {
    rhf.required =
      typeof rule.required === "string"
        ? rule.required
        : "This field is required";
  }
  if (rule.minLength) {
    rhf.minLength = {
      value: rule.minLength,
      message: rule.message || `Minimum ${rule.minLength} characters`,
    };
  }
  if (rule.maxLength) {
    rhf.maxLength = {
      value: rule.maxLength,
      message: rule.message || `Maximum ${rule.maxLength} characters`,
    };
  }
  if (rule.pattern) {
    rhf.pattern = {
      value: rule.pattern,
      message: rule.message || "Invalid value",
    };
  }
  if (typeof rule.validate === "function") {
    rhf.validate = rule.validate;
  }
  return rhf;
};

export default function AddForm({
  heading,
  subTitle,
  onBack,
  backLabel,
  title,
  fields = [],
  onSubmit,
  initialValues = {},
  onCancel,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  twoColumn = true,
  validationSchema,
  mode = "create", // "create", "edit", "view"
  onEdit, // Function to call when Edit button is clicked in view mode
  isSubmitting = false, // Loading state for submit button
}) {
  const router = useRouter();
  const isViewMode = mode === "view";
  const isEditMode = mode === "edit";
  const isCreateMode = mode === "create";

  const form = useForm({
    defaultValues: initialValues,
    resolver: validationSchema ? zodResolver(validationSchema) : undefined,
    mode: "onChange",
  });

  const handleCancel = () => {
    if (onCancel) return onCancel();
    router.back();
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    }
  };

  const gridCols = twoColumn
    ? "grid grid-cols-1 md:grid-cols-2 gap-6"
    : "grid grid-cols-1 gap-6";

  const renderFieldControl = (fieldDef, fieldApi) => {
    const { type, placeholder, options = [], disabled, editable } = fieldDef;
    // If editable is explicitly false, always disable the field
    const isFieldDisabled = disabled || editable === false || isViewMode;

    switch (type) {
      case "select": {
        // Filter out empty value options
        const filteredOptions = options.filter(
          (opt) => String(opt.value) !== ""
        );

        // Handle default value without useEffect
        const currentValue =
          fieldApi.value !== undefined && fieldApi.value !== ""
            ? fieldApi.value
            : filteredOptions.length > 0
            ? filteredOptions[0].value
            : "";

        const selectedOption = filteredOptions.find(
          (opt) => String(opt.value) === String(currentValue)
        );
        return (
          <DropdownMenu
            options={filteredOptions}
            selected={
              selectedOption
                ? selectedOption.value
                : filteredOptions[0]?.value || ""
            }
            label={
              selectedOption
                ? selectedOption.label
                : filteredOptions[0]?.label || placeholder || "Select..."
            }
            onSelect={(val) => fieldApi.onChange(val)}
            disabled={isFieldDisabled}
            buttonClassName="w-full px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-700 flex justify-between items-center"
            menuClassName="w-full"
          />
        );
      }
      case "multiselect":
        // Pill-style checkbox group for arrays (like services) - rendered horizontally
        return (
          <div className="flex flex-wrap gap-4 mt-2">
            {options.map((opt) => (
              <label
                key={opt.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={
                    Array.isArray(fieldApi.value)
                      ? fieldApi.value.includes(opt.value)
                      : false
                  }
                  onChange={(e) => {
                    let valArr = Array.isArray(fieldApi.value)
                      ? fieldApi.value
                      : [];
                    if (e.target.checked) {
                      if (!valArr.includes(opt.value))
                        valArr = [...valArr, opt.value];
                    } else {
                      valArr = valArr.filter((v) => v !== opt.value);
                    }
                    fieldApi.onChange(valArr);
                  }}
                  disabled={isFieldDisabled}
                  className="accent-indigo-600 h-4 w-4 cursor-pointer"
                />
                <span className="text-gray-700 text-sm">{opt.label}</span>
              </label>
            ))}
          </div>
        );
      case "textarea":
        return (
          <Textarea
            {...fieldApi}
            placeholder={placeholder}
            disabled={isFieldDisabled}
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
          />
        );
      case "checkbox":
        return (
          <div className="flex items-center gap-2 py-2">
            <Checkbox
              checked={!!fieldApi.value}
              onCheckedChange={(v) => fieldApi.onChange(!!v)}
              disabled={isFieldDisabled}
            />
            {placeholder && (
              <Label className="text-sm text-muted-foreground">
                {placeholder}
              </Label>
            )}
          </div>
        );
      case "number":
      case "email":
      case "password":
      case "date":
      case "text":
      default:
        return (
          <Input
            type={type || "text"}
            placeholder={placeholder}
            disabled={isFieldDisabled}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
            {...fieldApi}
          />
        );
    }
  };

  return (
    <div className="flex flex-col gap-6 max-w-7xl mx-auto px-4 py-6">
      <FormHeader
        title={heading}
        subtitle={subTitle}
        onBack={onBack}
        backLabel={backLabel}
      />

      <div className="bg-white rounded-xl shadow-md border border-gray-100">
        {title && (
          <div className="px-6 md:px-8 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {title}
            </h2>
          </div>
        )}
        <div className="p-6 md:p-8">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit((vals) => onSubmit?.(vals, form))}
              className="space-y-6"
            >
              {/* Fields grid */}
              <div className={gridCols}>
                {fields.map((f) => {
                  const spanClass =
                    twoColumn && f.colSpan === 2 ? "md:col-span-2" : "";
                  const rules = mapRulesToRHF(f.validation);
                  return (
                    <div key={f.name} className={spanClass}>
                      <FormField
                        control={form.control}
                        name={f.name}
                        rules={rules}
                        render={({ field }) => (
                          <FormItem>
                            {f.label && (
                              <FormLabel className="block text-sm font-medium text-gray-700 mb-1">
                                {f.label}
                              </FormLabel>
                            )}
                            <FormControl>
                              {typeof f.render === "function"
                                ? f.render(field, form)
                                : renderFieldControl(f, field)}
                            </FormControl>
                            {f.description && (
                              <p className="text-xs text-gray-500 mt-1">
                                {f.description}
                              </p>
                            )}
                            {/* Only show FormMessage if field doesn't have custom render or if it explicitly wants to show default error */}
                            {(typeof f.render !== "function" ||
                              f.showDefaultError !== false) && (
                              <FormMessage className="text-xs text-red-500 mt-1" />
                            )}
                          </FormItem>
                        )}
                      />
                    </div>
                  );
                })}
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed h-10 shadow-sm hover:shadow-md transition-all duration-300 ease-in-out"
                >
                  {isViewMode ? "Back" : cancelLabel}
                </Button>
                {isViewMode ? (
                  <Button
                    type="button"
                    onClick={handleEdit}
                    className="px-6 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed h-10 shadow-sm hover:shadow-md transition-all duration-300 ease-in-out"
                  >
                    Edit
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed h-10 shadow-sm hover:shadow-md transition-all duration-300 ease-in-out flex items-center justify-center gap-2 relative overflow-hidden"
                  >
                    {/* Smooth loading state transition */}
                    <span
                      className={`inline-flex items-center justify-center gap-2 transition-all duration-300 ease-in-out ${
                        isSubmitting
                          ? "opacity-0 scale-95"
                          : "opacity-100 scale-100"
                      }`}
                    >
                      {submitLabel}
                    </span>
                    <span
                      className={`absolute inset-0 flex items-center justify-center gap-2 transition-all duration-300 ease-in-out ${
                        isSubmitting
                          ? "opacity-100 scale-100"
                          : "opacity-0 scale-95"
                      }`}
                    >
                      {/* Smooth animated spinner */}
                      <svg
                        className="animate-spin h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8v8H4z"
                        ></path>
                      </svg>
                      <span className="whitespace-nowrap">
                        {submitLabel === "Create Organization" ||
                        submitLabel?.includes("Create")
                          ? "Creating..."
                          : submitLabel === "Create Client" ||
                            submitLabel?.includes("Client")
                          ? "Creating..."
                          : "Saving..."}
                      </span>
                    </span>
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
